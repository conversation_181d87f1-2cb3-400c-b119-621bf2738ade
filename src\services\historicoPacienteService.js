import axios from '@/services/axios';

/**
 * Busca todos os históricos de um paciente
 * @param {number} pacienteId - ID do paciente
 * @returns {Promise<Array>} - Lista de históricos
 */
export async function getHistoricosPaciente(pacienteId) {
  try {
    const response = await axios.get(`/historicos-pacientes?paciente_id=${pacienteId}`);
    return response.data;
  } catch (error) {
    console.error("Erro ao buscar históricos do paciente:", error);
    throw error;
  }
}

/**
 * Busca históricos de uma consulta específica
 * @param {number} pacienteId - ID do paciente
 * @param {number} consultaId - ID da consulta
 * @returns {Promise<Array>} - Lista de históricos da consulta
 */
export async function getHistoricosConsulta(pacienteId, consultaId) {
  try {
    const response = await axios.get(`/historicos-pacientes?paciente_id=${pacienteId}&consulta_id=${consultaId}`);
    return response.data;
  } catch (error) {
    console.error("Erro ao buscar históricos da consulta:", error);
    throw error;
  }
}

/**
 * Busca um histórico específico
 * @param {number} id - ID do histórico
 * @returns {Promise<Object>} - Dados do histórico
 */
export async function getHistoricoPaciente(id) {
  try {
    const response = await axios.get(`/historicos-pacientes/${id}`);
    return response.data;
  } catch (error) {
    console.error("Erro ao buscar histórico:", error);
    throw error;
  }
}

/**
 * Cria um novo histórico
 * @param {Object} historico - Dados do histórico
 * @returns {Promise<Object>} - Histórico criado
 */
export async function criarHistoricoPaciente(historico) {
  try {
    const response = await axios.post("/historicos-pacientes", historico);
    return response.data.data; // Retorna apenas os dados do histórico, não o wrapper
  } catch (error) {
    console.error("Erro ao criar histórico:", error);
    throw error;
  }
}

/**
 * Atualiza um histórico existente
 * @param {number} id - ID do histórico
 * @param {Object} historico - Dados atualizados do histórico
 * @returns {Promise<Object>} - Histórico atualizado
 */
export async function atualizarHistoricoPaciente(id, historico) {
  try {
    const response = await axios.put(`/historicos-pacientes/${id}`, historico);
    return response.data.data; // Retorna apenas os dados do histórico, não o wrapper
  } catch (error) {
    console.error("Erro ao atualizar histórico:", error);
    throw error;
  }
}

// Não usamos a função de exclusão direta, pois vamos apenas atualizar o registro
// removendo o item do array de modificações
